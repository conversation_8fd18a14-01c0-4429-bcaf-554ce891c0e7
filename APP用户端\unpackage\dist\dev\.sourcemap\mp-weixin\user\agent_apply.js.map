{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/agent_apply.vue?23f9", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/agent_apply.vue?c81a", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/agent_apply.vue?42f2", "uni-app:///user/agent_apply.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/agent_apply.vue?6e4e", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/agent_apply.vue?9c81"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "status", "loading", "applyInfo", "arr", "text", "color", "form", "typename", "type", "city", "cityId", "legalPersonName", "legalPersonIdCard", "legalPersonTel", "legalPersonIdCardImg1", "legalPersonIdCardImg2", "legalPersonLicense", "showMoney", "show", "columns", "title", "value", "columnsCity", "showCity", "watch", "handler", "methods", "getcity", "console", "id", "children", "uni", "icon", "confirmCity", "confirmType", "<PERSON><PERSON><PERSON><PERSON>", "columnIndex", "e", "index", "picker", "submit", "duration", "subForm", "url", "imgUpload", "imagelist", "imgtype", "seeDetails", "obj", "path", "setCityDisplayName", "onLoad"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoI;AACpI;AAC+D;AACL;AACsC;;;AAGhG;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,kGAAM;AACR,EAAE,2GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/CA;AAAA;AAAA;AAAA;AAA01B,CAAgB,02BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACkF92B;AAAA,eACA;EACA;EACA;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;MACA;QACAD;QACAC;MACA;QACAD;QACAC;MACA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC,UACA;QACAC;QACAC;MACA;QACAD;QACAC;MACA;QACAD;QACAC;MACA,GACA;MACAC,cACA,GACA;MACAC;IACA;EACA;EACAC;IACA;MACAC;QACA,qBACA;MAEA;IACA;EACA;EACAC;IACAC;MAAA;MACA;QACAC;QACA;QACA;;QAEA;QACA;UACAA;UACA;QACA;;QAEA;QACA;UACA;YAAA;cACAR;cACAS;cACAC;YACA;UAAA;QACA;;QAEA;QACA;QACA;QACAF;QAEA;UACA;UACA;UACA;YACA;YACA;YACA;YACAA;YAEA;cACA;cACA;cACA;gBACA;gBACA;gBACAA;cACA;YACA;UACA;YACA;YACA;cACA;cACA;gBACA;gBACA;gBACAA;gBAEA;kBACA;oBACA;oBACA;sBACA;sBACA;sBACAA;oBACA;kBACA;oBACAA;kBACA;gBACA;cACA;YACA;cACAA;YACA;UACA;QACA;MACA;QACAA;QACAG;UACAX;UACAY;QACA;MACA;IACA;IACAC;MAAA;MACAL;MACAA;MAEA;QACA;UACA;QACA;UACA;QACA;MACA;MAEA;QACA;UACA;QACA;UACA;QACA;MACA;QAAA;MAAA;;MAEAA;MACAA;MACA;IACA;IACAM;MACA;QACA,oBACA,GACA;MACA;QAAA;QACA,oBACA,IACA,GACA;MACA;QAAA;QACA,oBACA,IACA,IACA,GACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MAAA;MACA;MACA,IACAC,cAIAC,EAJAD;QACAE,QAGAD,EAHAC;QAAA,YAGAD,EADAE;QAAAA;MAGAX;QAAAQ;QAAAE;QAAAhB;MAAA;MAEA;QACA;QACA;QACA;UACA;YACA;YACA;cAAA;gBACAF;gBACAS;gBACAC;cACA;YAAA;YACA;YACAS;YAEA;cACA;cACA;gBAAA;kBACAnB;kBACAS;gBACA;cAAA;cACA;cACAU;YACA;UACA;YACA;YACA;cACA;cACA;cACAA;cACA;cACA;gBACA;kBACA;kBACA;kBACAA;gBACA;cACA;YACA;UACA;QACA;MACA;QACA;QACA;QACA;QACA;UACA;YACA;YACA;cAAA;gBACAnB;gBACAS;cACA;YAAA;YACA;YACAU;UACA;YACA;YACA;cACA;cACA;cACAA;YACA;UACA;QACA;MACA;IACA;IACAC;MACA;QACA;UACAT;YACAC;YACAZ;YACAqB;UACA;UACA;QACA;UACAV;YACAC;YACAZ;YACAqB;UACA;UACA;QACA;MACA;MACA;MACA;QACAV;UACAC;UACAZ;QACA;QACA;MACA;MACA;MACA;QACAW;UACAC;UACAZ;UACAqB;QACA;QACA;MACA;MACA;MAEAC;MACAA;MACAA;MACA;MACA;MACAd;MACA;QACAA;QACA;UACAG;YACAX;YACAY;UACA;UACAD;YACAY;UACA;QAEA;UACAZ;YACAX;YACAY;UACA;QAEA;MACA;IAEA;IACAY;MACA,IACAC,YAEAR,EAFAQ;QACAC,UACAT,EADAS;MAEA;IACA;IACAC;MAAA;MACA;QACA;UACA;UACAnB;;UAEA;UACA;;UAEA;UACAoB;YACAC;UACA;UACAD;YACAC;UACA;UACAD;YACAC;UACA;;UAEA;UACA,8CACA;YACAtC;YACAC;YAAA;YACAC;YACAL;YACAE;YACAI;YACAC;YACAC;UAAA,EACA;;UAEA;UACA;YACA;UACA;;UAEA;UACA;YACA;UACA;QACA;MACA;QACAY;MACA;IACA;IAEA;IACAsB;MACA;MACA;MACA;IACA;EACA;EACAC;IACA,mBACA;EACA;AAEA;AAAA,2B;;;;;;;;;;;;;AC3dA;AAAA;AAAA;AAAA;AAAimD,CAAgB,qjDAAG,EAAC,C;;;;;;;;;;;ACArnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "user/agent_apply.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './user/agent_apply.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./agent_apply.vue?vue&type=template&id=a02d1e04&scoped=true&\"\nvar renderjs\nimport script from \"./agent_apply.vue?vue&type=script&lang=js&\"\nexport * from \"./agent_apply.vue?vue&type=script&lang=js&\"\nimport style0 from \"./agent_apply.vue?vue&type=style&index=0&id=a02d1e04&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"a02d1e04\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"user/agent_apply.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./agent_apply.vue?vue&type=template&id=a02d1e04&scoped=true&\"", "var components\ntry {\n  components = {\n    uPicker: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-picker/u-picker\" */ \"uview-ui/components/u-picker/u-picker.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.show = false\n    }\n    _vm.e1 = function ($event) {\n      _vm.showCity = false\n    }\n    _vm.e2 = function ($event) {\n      _vm.show = true\n    }\n    _vm.e3 = function ($event) {\n      _vm.showCity = true\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./agent_apply.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./agent_apply.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"page\">\n\t\t<u-picker :show=\"show\" :columns=\"columns\" @cancel=\"show = false\" @confirm=\"confirmType\"\n\t\t\tkeyName=\"title\"></u-picker>\n\t\t<u-picker :show=\"showCity\" ref=\"uPicker\" :loading=\"loading\" :columns=\"columnsCity\" @change=\"changeHandler\"\n\t\t\tkeyName=\"title\" @cancel=\"showCity = false\" @confirm=\"confirmCity\"></u-picker>\n\t\t<view class=\"header\" :style=\"'color:'+arr[status].color\" v-if=\"status !== ''\">{{arr[status].text}}</view>\n\t\t<view class=\"main\">\n\t\t\t<view class=\"main_item\">\n\t\t\t\t<view class=\"title\"><span>*</span>法人姓名</view>\n\t\t\t\t<input type=\"text\" v-model=\"form.legalPersonName\" placeholder=\"请输入姓名\">\n\t\t\t</view>\n\t\t\t<view class=\"main_item\">\n\t\t\t\t<view class=\"title\"><span>*</span>法人身份证号</view>\n\t\t\t\t<input type=\"text\" v-model=\"form.legalPersonIdCard\" placeholder=\"请输入身份证号\">\n\t\t\t</view>\n\t\t\t<view class=\"main_item\">\n\t\t\t\t<view class=\"title\"><span>*</span>联系电话</view>\n\t\t\t\t<input type=\"text\" v-model=\"form.legalPersonTel\" placeholder=\"请输入联系电话\">\n\t\t\t</view>\n\t\t\t<view class=\"main_item\">\n\t\t\t\t<view class=\"title\"><span>*</span>选择省市区代理</view>\n\t\t\t\t<input type=\"text\" v-model=\"form.typename\" placeholder=\"请选择代理级别\" disabled @click=\"show = true\">\n\t\t\t</view>\n\t\t\t<view class=\"main_item\">\n\t\t\t\t<view class=\"title\"><span>*</span>选择区域</view>\n\t\t\t\t<input type=\"text\" v-model=\"form.city\" placeholder=\"请选择代理区域\" disabled @click=\"showCity = true\" >\n\t\t\t</view>\n\t\t\t<view class=\"main_item\">\n\t\t\t\t<view class=\"title\"><span>*</span>上传法人身份证照片</view>\n\t\t\t\t<view class=\"card\">\n\t\t\t\t\t<view class=\"card_item\">\n\t\t\t\t\t\t<view class=\"top\">\n\t\t\t\t\t\t\t<view class=\"das\">\n\t\t\t\t\t\t\t\t<view class=\"up\">\n\t\t\t\t\t\t\t\t\t<upload @upload=\"imgUpload\" :imagelist=\"form.legalPersonIdCardImg1\"\n\t\t\t\t\t\t\t\t\t\timgtype=\"legalPersonIdCardImg1\" imgclass=\"id_card_box\" text=\"身份证人像面\"\n\t\t\t\t\t\t\t\t\t\t:imgsize=\"1\"></upload>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"bottom\">拍摄人像面</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"card_item\">\n\t\t\t\t\t\t<view class=\"top\">\n\t\t\t\t\t\t\t<view class=\"das\">\n\t\t\t\t\t\t\t\t<view class=\"up\">\n\t\t\t\t\t\t\t\t\t<upload @upload=\"imgUpload\" :imagelist=\"form.legalPersonIdCardImg2\"\n\t\t\t\t\t\t\t\t\t\timgtype=\"legalPersonIdCardImg2\" imgclass=\"id_card_box\" text=\"身份证国徽面\"\n\t\t\t\t\t\t\t\t\t\t:imgsize=\"1\"></upload>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"bottom\">拍摄国徽面</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"main_item\">\n\t\t\t\t<view class=\"title\"><span>*</span>上传营业执照照片</view>\n\t\t\t\t<view class=\"big\">\n\t\t\t\t\t<view class=\"top\">\n\t\t\t\t\t\t<view class=\"das\">\n\t\t\t\t\t\t\t<view class=\"up\">\n\t\t\t\t\t\t\t\t<upload @upload=\"imgUpload\" :imagelist=\"form.legalPersonLicense\"\n\t\t\t\t\t\t\t\t\timgtype=\"legalPersonLicense\" imgclass=\"id_yy_box\" text=\"营业执照\" :imgsize=\"1\">\n\t\t\t\t\t\t\t\t</upload>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"bottom\">\n\t\t\t\t\t\t拍摄营业执照\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"footer\">\n\t\t\t<view class=\"btn\" @click=\"submit\" v-if=\"status !== 1 && status !== 0\">立即提交</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\t// import upload from '@/user/components/upload.vue';\n\texport default {\n\t\t// components: {\n\t\t//     upload\n\t\t//   },\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tstatus:'',\n\t\t\t\tloading: false,\n\t\t\t\tapplyInfo: null,\n\t\t\t\tarr: [{\n\t\t\t\t\ttext: '信息审核中，请稍作等待',\n\t\t\t\t\tcolor: '#FE921B'\n\t\t\t\t},  {\n\t\t\t\t\ttext: '审核成功',\n\t\t\t\t\tcolor: '#07C160'\n\t\t\t\t},{\n\t\t\t\t\ttext: '审核失败',\n\t\t\t\t\tcolor: '#E72427'\n\t\t\t\t},],\n\t\t\t\tform: {\n\t\t\t\t\ttypename: '省级',\n\t\t\t\t\ttype: 1,\n\t\t\t\t\tcity: \"\",\n\t\t\t\t\tcityId: [],\n\t\t\t\t\tlegalPersonName: '',\n\t\t\t\t\tlegalPersonIdCard: '',\n\t\t\t\t\tlegalPersonTel: '',\n\t\t\t\t\tlegalPersonIdCardImg1: [],\n\t\t\t\t\tlegalPersonIdCardImg2: [],\n\t\t\t\t\tlegalPersonLicense: []\n\t\t\t\t},\n\t\t\t\tshowMoney: false,\n\t\t\t\tshow: false,\n\t\t\t\tcolumns: [\n\t\t\t\t\t[{\n\t\t\t\t\t\ttitle: '省级',\n\t\t\t\t\t\tvalue: '1'\n\t\t\t\t\t}, {\n\t\t\t\t\t\ttitle: '市级',\n\t\t\t\t\t\tvalue: '2'\n\t\t\t\t\t}, {\n\t\t\t\t\t\ttitle: '区/县级代理',\n\t\t\t\t\t\tvalue: '3'\n\t\t\t\t\t}]\n\t\t\t\t],\n\t\t\t\tcolumnsCity: [\n\t\t\t\t\t[]\n\t\t\t\t],\n\t\t\t\tshowCity: false\n\t\t\t}\n\t\t},\n\t\twatch: {\n\t\t\t\"form.type\": {\n\t\t\t\thandler(nval) {\n\t\t\t\t\tthis.form.city = '',\n\t\t\t\t\t\tthis.form.cityId = []\n\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\tgetcity(e) {\n\t\t\t\tthis.$api.service.getCity(e).then(res => {\n\t\t\t\t\tconsole.log('getcity API response:', res);\n\t\t\t\t\t// 检查数据结构，兼容不同的返回格式\n\t\t\t\t\tlet cityData = res.data || res;\n\n\t\t\t\t\t// 确保cityData是数组\n\t\t\t\t\tif (!Array.isArray(cityData)) {\n\t\t\t\t\t\tconsole.error('City data is not an array:', cityData);\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\t// 标准化数据格式\n\t\t\t\t\tconst normalizeData = (data) => {\n\t\t\t\t\t\treturn data.map(item => ({\n\t\t\t\t\t\t\ttitle: item.trueName || item.title || item.name,\n\t\t\t\t\t\t\tid: item.id,\n\t\t\t\t\t\t\tchildren: item.children || []\n\t\t\t\t\t\t}));\n\t\t\t\t\t};\n\n\t\t\t\t\t// 处理省级数据\n\t\t\t\t\tconst provinces = normalizeData(cityData);\n\t\t\t\t\tthis.columnsCity[0] = provinces;\n\t\t\t\t\tconsole.log('Provinces loaded:', provinces);\n\n\t\t\t\t\tif (this.form.type > 1 && provinces.length > 0) {\n\t\t\t\t\t\t// 获取第一个省的城市数据\n\t\t\t\t\t\tconst firstProvince = provinces[0];\n\t\t\t\t\t\tif (firstProvince.children && firstProvince.children.length > 0) {\n\t\t\t\t\t\t\t// 如果省份数据包含children，直接使用\n\t\t\t\t\t\t\tconst cities = normalizeData(firstProvince.children);\n\t\t\t\t\t\t\tthis.columnsCity[1] = cities;\n\t\t\t\t\t\t\tconsole.log('Cities loaded from children:', cities);\n\n\t\t\t\t\t\t\tif (this.form.type > 2 && cities.length > 0) {\n\t\t\t\t\t\t\t\t// 获取第一个城市的区县数据\n\t\t\t\t\t\t\t\tconst firstCity = cities[0];\n\t\t\t\t\t\t\t\tif (firstCity.children && firstCity.children.length > 0) {\n\t\t\t\t\t\t\t\t\tconst districts = normalizeData(firstCity.children);\n\t\t\t\t\t\t\t\t\tthis.columnsCity[2] = districts;\n\t\t\t\t\t\t\t\t\tconsole.log('Districts loaded from children:', districts);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// 如果省份数据不包含children，通过API获取\n\t\t\t\t\t\t\tthis.$api.service.getCity(firstProvince.id).then(res1 => {\n\t\t\t\t\t\t\t\tconst cityData1 = res1.data || res1;\n\t\t\t\t\t\t\t\tif (Array.isArray(cityData1)) {\n\t\t\t\t\t\t\t\t\tconst cities = normalizeData(cityData1);\n\t\t\t\t\t\t\t\t\tthis.columnsCity[1] = cities;\n\t\t\t\t\t\t\t\t\tconsole.log('Cities loaded from API:', cities);\n\n\t\t\t\t\t\t\t\t\tif (this.form.type > 2 && cities.length > 0) {\n\t\t\t\t\t\t\t\t\t\tthis.$api.service.getCity(cities[0].id).then(res2 => {\n\t\t\t\t\t\t\t\t\t\t\tconst cityData2 = res2.data || res2;\n\t\t\t\t\t\t\t\t\t\t\tif (Array.isArray(cityData2)) {\n\t\t\t\t\t\t\t\t\t\t\t\tconst districts = normalizeData(cityData2);\n\t\t\t\t\t\t\t\t\t\t\t\tthis.columnsCity[2] = districts;\n\t\t\t\t\t\t\t\t\t\t\t\tconsole.log('Districts loaded from API:', districts);\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t}).catch(err => {\n\t\t\t\t\t\t\t\t\t\t\tconsole.error('Error loading districts:', err);\n\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}).catch(err => {\n\t\t\t\t\t\t\t\tconsole.error('Error loading cities:', err);\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}).catch(err => {\n\t\t\t\t\tconsole.error('getcity API error:', err);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '获取城市数据失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t})\n\t\t\t},\n\t\t\tconfirmCity(Array) {\n\t\t\t\tconsole.log('confirmCity Array:', Array);\n\t\t\t\tconsole.log('columnsCity:', this.columnsCity);\n\n\t\t\t\tthis.form.city = Array.value.map((item,index) => {\n\t\t\t\t\tif(item == undefined){\n\t\t\t\t\t\treturn this.columnsCity[index] && this.columnsCity[index][0] ? this.columnsCity[index][0].title : ''\n\t\t\t\t\t}else{\n\t\t\t\t\t\treturn item.title\n\t\t\t\t\t}\n\t\t\t\t}).join('-')\n\n\t\t\t\tthis.form.cityId = Array.value.map((e,j) => {\n\t\t\t\t\tif(e == undefined){\n\t\t\t\t\t\treturn this.columnsCity[j] && this.columnsCity[j][0] ? this.columnsCity[j][0].id : null\n\t\t\t\t\t}else{\n\t\t\t\t\t\treturn e.id\n\t\t\t\t\t}\n\t\t\t\t}).filter(id => id !== null) // 过滤掉null值\n\n\t\t\t\tconsole.log('Selected city:', this.form.city);\n\t\t\t\tconsole.log('Selected cityId:', this.form.cityId);\n\t\t\t\tthis.showCity = false\n\t\t\t},\n\t\t\tconfirmType(Array) {\n\t\t\t\tif (Array.value[0].value == 1) {\n\t\t\t\t\tthis.columnsCity = [\n\t\t\t\t\t\t[]\n\t\t\t\t\t]\n\t\t\t\t} else if (Array.value[0].value == 2) { //如果用户选择了市级\n\t\t\t\t\tthis.columnsCity = [\n\t\t\t\t\t\t[],\n\t\t\t\t\t\t[]\n\t\t\t\t\t]\n\t\t\t\t} else if (Array.value[0].value == 3) { //如果用户选择了区县级\n\t\t\t\t\tthis.columnsCity = [\n\t\t\t\t\t\t[],\n\t\t\t\t\t\t[],\n\t\t\t\t\t\t[]\n\t\t\t\t\t]\n\t\t\t\t}\n\t\t\t\tthis.form.typename = Array.value[0].title\n\t\t\t\tthis.form.type = Array.value[0].value\n\t\t\t\tthis.getcity(0)\n\t\t\t\tthis.show = false\n\t\t\t},\n\t\t\tchangeHandler(e) {\n\t\t\t\tif (this.form.type == 1) return\n\t\t\t\tconst {\n\t\t\t\t\tcolumnIndex,\n\t\t\t\t\tindex,\n\t\t\t\t\t// 微信小程序无法将picker实例传出来，只能通过ref操作\n\t\t\t\t\tpicker = this.$refs.uPicker\n\t\t\t\t} = e\n\n\t\t\t\tconsole.log('changeHandler:', { columnIndex, index, columnsCity: this.columnsCity });\n\n\t\t\t\tif (columnIndex === 0) {\n\t\t\t\t\t// 当选择省份时，更新城市列表\n\t\t\t\t\tconst selectedProvince = this.columnsCity[0] && this.columnsCity[0][index];\n\t\t\t\t\tif (selectedProvince) {\n\t\t\t\t\t\tif (selectedProvince.children) {\n\t\t\t\t\t\t\t// 如果有children属性，使用children数据\n\t\t\t\t\t\t\tconst cities = selectedProvince.children.map(item => ({\n\t\t\t\t\t\t\t\ttitle: item.trueName || item.title,\n\t\t\t\t\t\t\t\tid: item.id,\n\t\t\t\t\t\t\t\tchildren: item.children\n\t\t\t\t\t\t\t}));\n\t\t\t\t\t\t\tthis.columnsCity[1] = cities;\n\t\t\t\t\t\t\tpicker.setColumnValues(1, cities);\n\n\t\t\t\t\t\t\tif (this.form.type > 2 && cities.length > 0) {\n\t\t\t\t\t\t\t\t// 如果是区县级代理，同时更新区县列表\n\t\t\t\t\t\t\t\tconst districts = cities[0].children ? cities[0].children.map(item => ({\n\t\t\t\t\t\t\t\t\ttitle: item.trueName || item.title,\n\t\t\t\t\t\t\t\t\tid: item.id\n\t\t\t\t\t\t\t\t})) : [];\n\t\t\t\t\t\t\t\tthis.columnsCity[2] = districts;\n\t\t\t\t\t\t\t\tpicker.setColumnValues(2, districts);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// 如果没有children属性，通过API获取\n\t\t\t\t\t\t\tthis.$api.service.getCity(selectedProvince.id).then(res => {\n\t\t\t\t\t\t\t\tconst cities = Array.isArray(res) ? res : (res.data || []);\n\t\t\t\t\t\t\t\tthis.columnsCity[1] = cities;\n\t\t\t\t\t\t\t\tpicker.setColumnValues(1, cities);\n\t\t\t\t\t\t\t\tif (this.form.type == 2) return;\n\t\t\t\t\t\t\t\tif (cities.length > 0) {\n\t\t\t\t\t\t\t\t\tthis.$api.service.getCity(cities[0].id).then(res1 => {\n\t\t\t\t\t\t\t\t\t\tconst districts = Array.isArray(res1) ? res1 : (res1.data || []);\n\t\t\t\t\t\t\t\t\t\tthis.columnsCity[2] = districts;\n\t\t\t\t\t\t\t\t\t\tpicker.setColumnValues(2, districts);\n\t\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t} else if (columnIndex === 1) {\n\t\t\t\t\tif (this.form.type == 2) return\n\t\t\t\t\t// 当选择城市时，更新区县列表\n\t\t\t\t\tconst selectedCity = this.columnsCity[1] && this.columnsCity[1][index];\n\t\t\t\t\tif (selectedCity) {\n\t\t\t\t\t\tif (selectedCity.children) {\n\t\t\t\t\t\t\t// 如果有children属性，使用children数据\n\t\t\t\t\t\t\tconst districts = selectedCity.children.map(item => ({\n\t\t\t\t\t\t\t\ttitle: item.trueName || item.title,\n\t\t\t\t\t\t\t\tid: item.id\n\t\t\t\t\t\t\t}));\n\t\t\t\t\t\t\tthis.columnsCity[2] = districts;\n\t\t\t\t\t\t\tpicker.setColumnValues(2, districts);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// 如果没有children属性，通过API获取\n\t\t\t\t\t\t\tthis.$api.service.getCity(selectedCity.id).then(res => {\n\t\t\t\t\t\t\t\tconst districts = Array.isArray(res) ? res : (res.data || []);\n\t\t\t\t\t\t\t\tthis.columnsCity[2] = districts;\n\t\t\t\t\t\t\t\tpicker.setColumnValues(2, districts);\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\tsubmit() {\n\t\t\t\tfor (let key in this.form) {\n\t\t\t\t\tif (this.form[key] == '') {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\ttitle: '请填写完整提交',\n\t\t\t\t\t\t\tduration: 1000\n\t\t\t\t\t\t})\n\t\t\t\t\t\treturn\n\t\t\t\t\t} else if (typeof this.form[key] == 'object' && this.form[key].length == 0) {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\ttitle: '请填写完整提交',\n\t\t\t\t\t\t\tduration: 1000\n\t\t\t\t\t\t})\n\t\t\t\t\t\treturn\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tlet p = /^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$/;\n\t\t\t\tif (p.test(this.form.legalPersonIdCard) == false) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '请填写正确的身份证号'\n\t\t\t\t\t})\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\tlet phoneReg = /^1[3456789]\\d{9}$/\n\t\t\t\tif (!phoneReg.test(this.form.legalPersonTel)) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '请填写正确的手机号',\n\t\t\t\t\t\tduration: 1000\n\t\t\t\t\t})\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\tlet subForm = JSON.parse(JSON.stringify(this.form))\n\t\t\t\t\n\t\t\t\tsubForm.legalPersonIdCardImg1 = subForm.legalPersonIdCardImg1[0].path\n\t\t\t\tsubForm.legalPersonIdCardImg2 = subForm.legalPersonIdCardImg2[0].path\n\t\t\t\tsubForm.legalPersonLicense =subForm.legalPersonLicense[0].path\n\t\t\t\tdelete subForm.city\n\t\t\t\tdelete subForm.typename\n\t\t\t\tconsole.log(subForm)\n\t\t\t\tthis.$api.service.dlApply(subForm).then(res => {\n\t\t\t\t\tconsole.log(res)\n\t\t\t\t\tif (res.code==='200') {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle:res.msg,\n\t\t\t\t\t\t\ticon: \"success\",\n\t\t\t\t\t\t});\n\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\turl: '/pages/apply_over'\n\t\t\t\t\t\t})\n\t\t\t\t\t\t\n\t\t\t\t\t}else{\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle:res.msg,\n\t\t\t\t\t\t\ticon: \"none\",\n\t\t\t\t\t\t});\n\t\t\t\t\t\t\n\t\t\t\t\t}\n\t\t\t\t})\n\n\t\t\t},\n\t\t\timgUpload(e) {\n\t\t\t\tlet {\n\t\t\t\t\timagelist,\n\t\t\t\t\timgtype\n\t\t\t\t} = e;\n\t\t\t\tthis.form[imgtype] = imagelist;\n\t\t\t},\n\t\t\tseeDetails() {\n\t\t\t\tthis.$api.service.dlSee().then(res => {\n\t\t\t\t\tif (res && res.data) {\n\t\t\t\t\t\tlet obj = res.data\n\t\t\t\t\t\tconsole.log('代理申请详情数据:', obj);\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 设置状态\n\t\t\t\t\t\tthis.status = obj.status\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 处理图片字段\n\t\t\t\t\t\tobj.legalPersonIdCardImg1 = obj.legalPersonIdcardImg1 ? [{\n\t\t\t\t\t\t\tpath: obj.legalPersonIdcardImg1\n\t\t\t\t\t\t}] : []\n\t\t\t\t\t\tobj.legalPersonIdCardImg2 = obj.legalPersonIdcardImg2 ? [{\n\t\t\t\t\t\t\tpath: obj.legalPersonIdcardImg2\n\t\t\t\t\t\t}] : []\n\t\t\t\t\t\tobj.legalPersonLicense = obj.legalPersonLicense ? [{\n\t\t\t\t\t\t\tpath: obj.legalPersonLicense\n\t\t\t\t\t\t}] : []\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 映射表单字段\n\t\t\t\t\t\tthis.form = {\n\t\t\t\t\t\t\t...this.form,\n\t\t\t\t\t\t\tlegalPersonName: obj.legalPersonName || '',\n\t\t\t\t\t\t\tlegalPersonIdCard: obj.legalPersonIdcard || '', // 注意字段名差异\n\t\t\t\t\t\t\tlegalPersonTel: obj.legalPersonTel || '',\n\t\t\t\t\t\t\ttype: obj.type || 1,\n\t\t\t\t\t\t\tcityId: obj.cityId ? obj.cityId.split(',') : [],\n\t\t\t\t\t\t\tlegalPersonIdCardImg1: obj.legalPersonIdCardImg1,\n\t\t\t\t\t\t\tlegalPersonIdCardImg2: obj.legalPersonIdCardImg2,\n\t\t\t\t\t\t\tlegalPersonLicense: obj.legalPersonLicense\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 设置代理类型显示名称\n\t\t\t\t\t\tif (this.form.type && this.form.type >= 1 && this.form.type <= 3) {\n\t\t\t\t\t\t\tthis.form.typename = this.columns[0][this.form.type-1].title\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 根据cityId设置城市显示名称\n\t\t\t\t\t\tif (this.form.cityId && this.form.cityId.length > 0) {\n\t\t\t\t\t\t\tthis.setCityDisplayName()\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}).catch((err)=>{\n\t\t\t\t\tconsole.log('获取代理申请详情失败:', err);\n\t\t\t\t})\n\t\t\t},\n\t\t\t\n\t\t\t// 新增方法：根据cityId设置城市显示名称\n\t\t\tsetCityDisplayName() {\n\t\t\t\t// 这里可以根据cityId查询对应的城市名称\n\t\t\t\t// 暂时先设置为空，等城市数据加载完成后再处理\n\t\t\t\tthis.form.city = ''\n\t\t\t}\n\t\t},\n\t\tonLoad() {\n\t\t\tthis.seeDetails(),\n\t\t\tthis.getcity(0)\n\t\t},\n\t\t\n\t}\n</script>\n\n<style scoped lang=\"scss\">\n\t.page {\n\t\tpadding-bottom: 200rpx;\n\n\t\t.header {\n\t\t\twidth: 750rpx;\n\t\t\theight: 58rpx;\n\t\t\tbackground: #FFF7F1;\n\t\t\tline-height: 58rpx;\n\t\t\ttext-align: center;\n\t\t\tfont-size: 28rpx;\n\t\t\tfont-weight: 400;\n\t\t}\n\n\t\t.main {\n\t\t\tpadding: 40rpx 30rpx;\n\n\t\t\t.main_item {\n\t\t\t\tmargin-bottom: 20rpx;\n\n\t\t\t\t.title {\n\t\t\t\t\tmargin-bottom: 20rpx;\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\tfont-weight: 400;\n\t\t\t\t\tcolor: #333333;\n\n\t\t\t\t\tspan {\n\t\t\t\t\t\tcolor: #E72427;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tinput {\n\t\t\t\t\twidth: 690rpx;\n\t\t\t\t\theight: 110rpx;\n\t\t\t\t\tbackground: #F8F8F8;\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\tfont-weight: 400;\n\t\t\t\t\tline-height: 110rpx;\n\t\t\t\t\tpadding: 0 40rpx;\n\t\t\t\t\tbox-sizing: border-box;\n\t\t\t\t}\n\n\t\t\t\t.big {\n\t\t\t\t\twidth: 690rpx;\n\t\t\t\t\theight: 388rpx;\n\t\t\t\t\tbackground: #F2FAFE;\n\t\t\t\t\tborder-radius: 16rpx 16rpx 16rpx 16rpx;\n\n\t\t\t\t\t.top {\n\t\t\t\t\t\theight: 322rpx;\n\t\t\t\t\t\tpadding-top: 20rpx;\n\n\t\t\t\t\t\t.das {\n\t\t\t\t\t\t\tmargin: 0 auto;\n\t\t\t\t\t\t\twidth: 632rpx;\n\t\t\t\t\t\t\theight: 284rpx;\n\t\t\t\t\t\t\tborder-radius: 0rpx 0rpx 0rpx 0rpx;\n\t\t\t\t\t\t\tborder: 2rpx dashed #2E80FE;\n\t\t\t\t\t\t\tpadding-top: 14rpx;\n\n\t\t\t\t\t\t\t.up {\n\t\t\t\t\t\t\t\tmargin: 0 auto;\n\t\t\t\t\t\t\t\twidth: 594rpx;\n\t\t\t\t\t\t\t\theight: 258rpx;\n\t\t\t\t\t\t\t\tbackground: rgba(0, 0, 0, 0.4);\n\t\t\t\t\t\t\t\tborder-radius: 12rpx 12rpx 12rpx 12rpx;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t.bottom {\n\t\t\t\t\t\theight: 66rpx;\n\t\t\t\t\t\twidth: 690rpx;\n\t\t\t\t\t\theight: 66rpx;\n\t\t\t\t\t\tbackground: #2E80FE;\n\t\t\t\t\t\tborder-radius: 0rpx 0rpx 16rpx 16rpx;\n\t\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\t\tfont-weight: 400;\n\t\t\t\t\t\tcolor: #FFFFFF;\n\t\t\t\t\t\tline-height: 66rpx;\n\t\t\t\t\t\ttext-align: center;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.card {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tjustify-content: space-between;\n\n\t\t\t\t\t.card_item {\n\t\t\t\t\t\twidth: 332rpx;\n\t\t\t\t\t\theight: 332rpx;\n\t\t\t\t\t\tbackground: #F2FAFE;\n\t\t\t\t\t\tborder-radius: 16rpx 16rpx 16rpx 16rpx;\n\t\t\t\t\t\toverflow: hidden;\n\n\t\t\t\t\t\t.top {\n\t\t\t\t\t\t\theight: 266rpx;\n\t\t\t\t\t\t\twidth: 100%;\n\t\t\t\t\t\t\tpadding-top: 40rpx;\n\n\t\t\t\t\t\t\t.das {\n\t\t\t\t\t\t\t\tmargin: 0 auto;\n\t\t\t\t\t\t\t\twidth: 266rpx;\n\t\t\t\t\t\t\t\theight: 180rpx;\n\t\t\t\t\t\t\t\tborder: 2rpx dashed #2E80FE;\n\t\t\t\t\t\t\t\tpadding-top: 28rpx;\n\n\t\t\t\t\t\t\t\t.up {\n\t\t\t\t\t\t\t\t\tmargin: 0 auto;\n\t\t\t\t\t\t\t\t\twidth: 210rpx;\n\t\t\t\t\t\t\t\t\theight: 130rpx;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t.bottom {\n\t\t\t\t\t\t\theight: 66rpx;\n\t\t\t\t\t\t\twidth: 100%;\n\t\t\t\t\t\t\tbackground-color: #2E80FE;\n\t\t\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\t\t\tfont-weight: 400;\n\t\t\t\t\t\t\tcolor: #FFFFFF;\n\t\t\t\t\t\t\ttext-align: center;\n\t\t\t\t\t\t\tline-height: 66rpx;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.footer {\n\t\t\tpadding: 52rpx 30rpx;\n\t\t\twidth: 750rpx;\n\t\t\tbackground: #FFFFFF;\n\t\t\tbox-shadow: 0rpx 0rpx 6rpx 2rpx rgba(193, 193, 193, 0.3);\n\t\t\tposition: fixed;\n\t\t\tbottom: 0;\n\n\t\t\t.btn {\n\t\t\t\twidth: 690rpx;\n\t\t\t\theight: 98rpx;\n\t\t\t\tbackground: #2E80FE;\n\t\t\t\tborder-radius: 50rpx 50rpx 50rpx 50rpx;\n\t\t\t\tfont-size: 32rpx;\n\t\t\t\tfont-weight: 500;\n\t\t\t\tcolor: #FFFFFF;\n\t\t\t\tline-height: 98rpx;\n\t\t\t\ttext-align: center;\n\t\t\t}\n\t\t}\n\t}\n</style>", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./agent_apply.vue?vue&type=style&index=0&id=a02d1e04&scoped=true&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./agent_apply.vue?vue&type=style&index=0&id=a02d1e04&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755673022601\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}